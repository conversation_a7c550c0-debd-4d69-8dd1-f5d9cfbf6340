import { createServerSupabaseClient } from './server';
import { Documento } from './supabaseClient';
import { downloadFileContentFromServer } from './storageService.server';

/**
 * Guarda un nuevo documento en la base de datos asociado al usuario actual (versión servidor)
 */
export async function guardarDocumentoServer(documento: Omit<Documento, 'creado_en' | 'actualizado_en' | 'user_id'> & { id?: string }): Promise<string | null> {
  try {
    // Crear cliente de Supabase para el servidor
    const supabase = await createServerSupabaseClient();
    
    // Obtener el usuario actual
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      console.error('No hay usuario autenticado:', userError?.message);
      return null;
    }

    // Añadir el user_id al documento
    const documentoConUsuario = {
      ...documento,
      user_id: user.id,
    };

    const { data, error } = await supabase
      .from('documentos')
      .insert([documentoConUsuario])
      .select();

    if (error) {
      console.error('Error al guardar documento:', error);
      return null;
    }

    return data?.[0]?.id || null;
  } catch (error) {
    console.error('Error al guardar documento:', error);
    return null;
  }
}

/**
 * Obtiene el contenido de una lista de documentos, manejando tanto documentos en BD como en Storage
 * FUNCIÓN CRÍTICA PARA EL FLUJO CONSISTENTE DE TODAS LAS HERRAMIENTAS DE IA
 */
export async function obtenerContenidoDocumentos(documentos: any[]): Promise<string[]> {
  console.log('📄 [obtenerContenidoDocumentos] Procesando', documentos.length, 'documentos');

  const contenidos: string[] = [];

  for (const documento of documentos) {
    try {
      let contenido = '';

      // 1. Prioridad: Storage (para documentos grandes)
      if (documento.storage_path) {
        console.log('📁 [Storage] Descargando:', documento.storage_path);
        const contenidoStorage = await downloadFileContentFromServer(documento.storage_path);
        if (contenidoStorage) {
          contenido = contenidoStorage;
          console.log('✅ [Storage] Descargado exitosamente:', contenido.length, 'caracteres');
        } else {
          console.warn('⚠️ [Storage] No se pudo descargar, usando fallback');
        }
      }

      // 2. Fallback: Contenido en BD
      if (!contenido) {
        if (documento.contenido_corto) {
          contenido = documento.contenido_corto;
          console.log('📝 [BD] Usando contenido_corto:', contenido.length, 'caracteres');
        } else if (documento.contenido) {
          contenido = documento.contenido;
          console.log('📝 [BD] Usando contenido:', contenido.length, 'caracteres');
        }
      }

      // 3. Validación final
      if (!contenido || contenido.trim().length === 0) {
        console.error('❌ [Error] Documento sin contenido:', documento.titulo || documento.id);
        throw new Error(`El documento "${documento.titulo || documento.id}" no tiene contenido disponible`);
      }

      contenidos.push(contenido);
      console.log('✅ [Procesado]', documento.titulo || documento.id, ':', contenido.length, 'caracteres');

    } catch (error) {
      console.error('❌ [Error] Procesando documento:', documento.titulo || documento.id, error);
      throw error;
    }
  }

  console.log('🎉 [obtenerContenidoDocumentos] Completado:', contenidos.length, 'documentos procesados');
  return contenidos;
}
