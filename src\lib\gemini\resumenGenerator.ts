// src/lib/gemini/resumenGenerator.ts
// NINGÚN CAMBIO NECESARIO AQUÍ. EL CÓDIGO ESTÁ CORRECTO.

import { SummaryOrchestrator } from '../services/summaryOrchestrator';
import { PROMPT_RESUMEN_FINAL_CONSOLIDACION } from '../../config/prompts';
import { llamarOpenAI } from '../openai/openaiClient';
import { getOpenAIConfig } from '@/config/openai';
import { type Documento } from '@/types';
import { CHUNKING_LIMITS } from '@/config/chunking';

/**
 * Genera un resumen de un documento, actuando como un despachador que elige
 * entre un procesamiento directo (para documentos cortos) o un flujo Map-Reduce
 * orquestado (para documentos largos).
 */
export async function generarResumen(
  documento: Documento, // La clave es que el objeto que se pase aquí debe ser un 'Documento' completo
  instrucciones?: string
): Promise<string> {
  try {
    // 1. Validar entrada
    if (!documento) {
      throw new Error("El documento proporcionado no es válido.");
    }
    // Añadir validación para contenido o storage_path
    if (!documento.contenido && !documento.contenido_corto && !documento.storage_path) {
      throw new Error("El documento no tiene contenido ni una ruta de almacenamiento válida.");
    }

    // 2. Decidir la estrategia: Vía rápida vs. Orquestador
    const umbralParaChunking = CHUNKING_LIMITS.MIN_SIZE_FOR_CHUNKING || 20000;

    // Obtener el tamaño del contenido para la decisión
    // Nota: Esto es una simplificación. Idealmente, para storage_path, se usaría file_size_bytes.
    const contenidoParaChequeo = documento.contenido || documento.contenido_corto || '';

    if (contenidoParaChequeo.length > 0 && contenidoParaChequeo.length < umbralParaChunking && !documento.storage_path) {
      // --- VÍA RÁPIDA PARA DOCUMENTOS CORTOS EN BD ---
      console.log(`⚡️ Documento corto detectado en BD. Usando vía rápida para resumen.`);

      const prompt = PROMPT_RESUMEN_FINAL_CONSOLIDACION
        .replace('{texto_largo_combinado}', contenidoParaChequeo);

      const config = getOpenAIConfig('RESUMENES');
      const messages = [{ role: 'user' as const, content: prompt }];

      const resumenDirecto = await llamarOpenAI(messages, {
        ...config,
        activityName: 'Resumen (Vía Rápida)'
      });

      console.log('✅ Resumen (Vía Rápida) generado exitosamente.');
      return resumenDirecto;

    } else {
      // --- FLUJO ORQUESTADO PARA DOCUMENTOS LARGOS O EN STORAGE ---
      console.log(`🧠 Documento largo o en Storage detectado. Delegando a SummaryOrchestrator.`);

      // La llamada ahora es asíncrona
      return await SummaryOrchestrator.generateHierarchicalSummary(documento, instrucciones);
    }

  } catch (error) {
    console.error('Error en el despachador de generarResumen:', error);
    if (error instanceof Error) {
      throw new Error(`Error al generar el resumen: ${error.message}`);
    }
    throw new Error("Ha ocurrido un error inesperado durante la generación del resumen.");
  }
}