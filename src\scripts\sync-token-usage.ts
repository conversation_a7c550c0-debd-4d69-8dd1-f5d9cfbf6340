// Script para sincronizar el uso de tokens entre user_token_usage y user_profiles
// Este script corrige la discrepancia entre los datos reales y el perfil del usuario

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

interface UserProfile {
  user_id: string;
  current_month_tokens: number;
  monthly_token_limit: number;
  current_month: string;
  subscription_plan: string;
}

interface TokenUsageSummary {
  user_id: string;
  total_tokens: number;
  record_count: number;
}

async function syncTokenUsage() {
  console.log('🔄 Iniciando sincronización de uso de tokens...');

  try {
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
    console.log(`📅 Mes actual: ${currentMonth}`);

    // 1. Obtener el uso real de tokens por usuario para el mes actual
    const { data: tokenUsage, error: usageError } = await supabase
      .from('user_token_usage')
      .select('user_id, total_tokens')
      .eq('usage_month', currentMonth);

    if (usageError) {
      console.error('❌ Error obteniendo uso de tokens:', usageError);
      return;
    }

    // 2. Agrupar por usuario
    const userUsage = tokenUsage.reduce((acc: Record<string, TokenUsageSummary>, record) => {
      if (!acc[record.user_id]) {
        acc[record.user_id] = {
          user_id: record.user_id,
          total_tokens: 0,
          record_count: 0
        };
      }
      acc[record.user_id].total_tokens += record.total_tokens;
      acc[record.user_id].record_count += 1;
      return acc;
    }, {});

    console.log(`📊 Encontrados ${Object.keys(userUsage).length} usuarios con uso de tokens`);

    // 3. Obtener perfiles de usuarios
    const { data: profiles, error: profilesError } = await supabase
      .from('user_profiles')
      .select('user_id, current_month_tokens, monthly_token_limit, current_month, subscription_plan');

    if (profilesError) {
      console.error('❌ Error obteniendo perfiles:', profilesError);
      return;
    }

    console.log(`👥 Encontrados ${profiles.length} perfiles de usuario`);

    // 4. Identificar discrepancias y corregir
    const corrections = [];
    
    for (const profile of profiles) {
      const realUsage = userUsage[profile.user_id];
      
      if (realUsage) {
        const currentProfileTokens = profile.current_month === currentMonth 
          ? profile.current_month_tokens 
          : 0;
        
        const discrepancy = realUsage.total_tokens - currentProfileTokens;
        
        if (Math.abs(discrepancy) > 0) {
          corrections.push({
            user_id: profile.user_id,
            current_profile_tokens: currentProfileTokens,
            real_usage: realUsage.total_tokens,
            discrepancy: discrepancy,
            record_count: realUsage.record_count,
            plan: profile.subscription_plan
          });
        }
      }
    }

    console.log(`🔍 Encontradas ${corrections.length} discrepancias`);

    // 5. Mostrar discrepancias
    if (corrections.length > 0) {
      console.log('\n📋 Discrepancias encontradas:');
      corrections.forEach((correction, index) => {
        console.log(`${index + 1}. Usuario: ${correction.user_id}`);
        console.log(`   Plan: ${correction.plan}`);
        console.log(`   Perfil: ${correction.current_profile_tokens.toLocaleString()} tokens`);
        console.log(`   Real: ${correction.real_usage.toLocaleString()} tokens`);
        console.log(`   Discrepancia: ${correction.discrepancy.toLocaleString()} tokens`);
        console.log(`   Registros: ${correction.record_count}`);
        console.log('');
      });

      // 6. Preguntar si corregir (en un entorno real, esto sería un parámetro)
      const shouldCorrect = true; // Cambiar a false para solo mostrar discrepancias

      if (shouldCorrect) {
        console.log('🔧 Corrigiendo discrepancias...');
        
        for (const correction of corrections) {
          const { error: updateError } = await supabase
            .from('user_profiles')
            .update({
              current_month_tokens: correction.real_usage,
              current_month: currentMonth,
              updated_at: new Date().toISOString()
            })
            .eq('user_id', correction.user_id);

          if (updateError) {
            console.error(`❌ Error actualizando usuario ${correction.user_id}:`, updateError);
          } else {
            console.log(`✅ Corregido usuario ${correction.user_id}: ${correction.current_profile_tokens} → ${correction.real_usage} tokens`);
          }
        }
        
        console.log('🎉 Sincronización completada');
      } else {
        console.log('ℹ️ Modo solo lectura - no se realizaron correcciones');
      }
    } else {
      console.log('✅ No se encontraron discrepancias - todos los datos están sincronizados');
    }

    // 7. Resumen final
    const totalRealUsage = Object.values(userUsage).reduce((sum, user) => sum + user.total_tokens, 0);
    const totalProfileUsage = profiles.reduce((sum, profile) => {
      const tokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;
      return sum + tokens;
    }, 0);

    console.log('\n📊 Resumen:');
    console.log(`Total real (user_token_usage): ${totalRealUsage.toLocaleString()} tokens`);
    console.log(`Total perfiles (user_profiles): ${totalProfileUsage.toLocaleString()} tokens`);
    console.log(`Diferencia total: ${(totalRealUsage - totalProfileUsage).toLocaleString()} tokens`);

  } catch (error) {
    console.error('💥 Error en sincronización:', error);
  }
}

// Función para verificar un usuario específico
async function checkSpecificUser(userId: string) {
  console.log(`🔍 Verificando usuario específico: ${userId}`);
  
  const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
  
  // Obtener uso real
  const { data: tokenUsage, error: usageError } = await supabase
    .from('user_token_usage')
    .select('total_tokens, activity_type, created_at')
    .eq('user_id', userId)
    .eq('usage_month', currentMonth)
    .order('created_at', { ascending: false });

  if (usageError) {
    console.error('❌ Error:', usageError);
    return;
  }

  const totalReal = tokenUsage.reduce((sum, record) => sum + record.total_tokens, 0);

  // Obtener perfil
  const { data: profile, error: profileError } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('user_id', userId)
    .single();

  if (profileError) {
    console.error('❌ Error:', profileError);
    return;
  }

  const profileTokens = profile.current_month === currentMonth ? profile.current_month_tokens : 0;

  console.log(`📊 Resultados para ${userId}:`);
  console.log(`Plan: ${profile.subscription_plan}`);
  console.log(`Límite: ${profile.monthly_token_limit.toLocaleString()}`);
  console.log(`Perfil: ${profileTokens.toLocaleString()} tokens`);
  console.log(`Real: ${totalReal.toLocaleString()} tokens`);
  console.log(`Discrepancia: ${(totalReal - profileTokens).toLocaleString()} tokens`);
  console.log(`Registros: ${tokenUsage.length}`);
  console.log(`Última actualización perfil: ${profile.updated_at}`);
}

// Ejecutar según el argumento
const userId = process.argv[2];
if (userId) {
  checkSpecificUser(userId);
} else {
  syncTokenUsage();
}

export { syncTokenUsage, checkSpecificUser };
